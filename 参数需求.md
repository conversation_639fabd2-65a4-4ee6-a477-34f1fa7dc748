始终用中文和用户进行沟通！

思政课虚拟仿真实验教学管理系统-静态演示前台参数
一、门户官网
1、门户官网包含图片轮播、中心介绍、项目介绍、友情链接等板块。管理后台中可以根据实际需要修改相关板块内容，比如图片轮播的图片、中心介绍的文字内容、项目介绍的项目、友情链接中的链接。

二、教师后台
1、教师后台包含个人信息模块、班级管理模块、实验安排模块、成绩管理模块等功能模块；

2、教师后台能够实现学生信息和教学班级管理、在规定时间内安排一个或多个虚拟仿真实验、查看学生实验成绩、批改学生实验报告等功能；

##
现在需要开发一个满足上面参数的系统。
1、门户官网。一个虚拟仿真使用平台的门户官网，需要又图片轮播、中心介绍、项目介绍、友情链接板块。
2、教师后台。
2.1门户官网管理模块，可以管理官网的图片轮播的图片、中心介绍的文字内容、项目介绍的项目、友情链接中的链接。
2.2 虚拟仿真实验管理平台的常见模块。
2.2.1个人信息、班级管理、实验安排、成绩管理模块。
2.2.2 能够实现学生信息和教学班级管理、在规定时间内安排一个或多个虚拟仿真实验、查看学生实验成绩、批改学生实验报告等功能；

开发注意
1、这个前端系统不会投入实际使用，只是为了演示满足有这些参数，所以开发时记住最小实现原则，不要额外增加功能。
2、前端系统不会有后端数据，请你直接把数据和图片等固定在vue页面或必要的文件中。但是要能够完整的实现这些流程和数据，不能有中途阻断性问题。
那么数据如何存储呢，比如存储在localStorage中，演示完毕有一个按钮可以清除这些临时保存的数据。
3、技术栈 vue3(但是选项式api)、fontawesome、ant-design-vue 4、主题色为黑白红。

现在请你在当前页面一次性完成上述需求开发、不要测试用例、不要npm调试。